import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:responsive_grid_list/responsive_grid_list.dart';
import 'package:example/data/models/bet.dart';
import 'package:example/data/models/bet_raw.dart';
import 'package:example/data/providers/simple_providers.dart';
import 'package:example/util/format_utils.dart';
import 'package:example/util/util.dart';
import 'package:example/widgets/scaffold/app_scaffold.dart';

import '../theme/app_colors.dart';
import '../theme/app_typography.dart';
import 'sell_screen.dart';

class LedgerScreen extends ConsumerWidget {
  const LedgerScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final betsStream = ref.watch(currentBetsProvider);
    final fighersStream = ref.watch(allFightersProvider);
    final selectedMatch = ref.watch(selectedMatchProvider);

    return selectedMatch == null
        ? _buildNoMatchSelectedState()
        : fighersStream.when(
          data:
              (fighters) => betsStream.when(
                data: (bets) {
                  Map<String, int> ledger = {};
                  for (Bet b in bets) {
                    RBetModel betRaw = generateTwo("${b.number}=${b.amount}");
                    bool isDealer =
                        fighters.firstWhere((f) => f.id == b.fighter).isDealer;
                    for (IBetModel bet in betRaw.bets) {
                      ledger.containsKey(bet.number)
                          ? ledger[bet.number] =
                              ledger[bet.number]! +
                              (isDealer ? -bet.amount : bet.amount)
                          : ledger[bet.number] =
                              isDealer ? -bet.amount : bet.amount;
                    }
                  }
                  //sort
                  ledger = Map.fromEntries(
                    ledger.entries.toList()
                      ..sort((a, b) => a.key.compareTo(b.key)),
                  );

                  return AppScaffold(
                    title: 'Ledger',
                    actions: [
                      TextButton.icon(
                        icon: const Icon(Icons.arrow_forward),
                        label: const Text('Over'),
                        style: ButtonStyle(
                          foregroundColor: WidgetStateProperty.all(
                            Colors.white,
                          ),
                          backgroundColor: WidgetStateProperty.all(
                            AppColors.error,
                          ),
                        ),
                        onPressed: () async {
                          await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) => SellScreen(
                                    ledger.entries
                                        .map(
                                          (e) => IBetModel(
                                            number: e.key,
                                            amount: e.value,
                                          ),
                                        )
                                        .where(
                                          (e) =>
                                              e.amount >
                                              int.parse(selectedMatch.brake),
                                        )
                                        .map(
                                          (e) => IBetModel(
                                            number: e.number,
                                            amount:
                                                e.amount -
                                                int.parse(selectedMatch.brake),
                                          ),
                                        )
                                        .toList(),
                                    fighters,
                                  ),
                            ),
                          );
                          await Future.delayed(
                            const Duration(milliseconds: 500),
                          );
                          ref.invalidate(currentBetsProvider);
                        },
                      ),
                      SizedBox(width: 8),
                    ],
                    body: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        children: [
                          Expanded(
                            child: ResponsiveGridList(
                              horizontalGridSpacing: 2,
                              verticalGridSpacing: 2,
                              minItemWidth: 100,
                              minItemsPerRow: 3,
                              maxItemsPerRow: 10,
                              listViewBuilderOptions: ListViewBuilderOptions(),
                              children:
                                  ledger.entries
                                      .map(
                                        (e) => IBetModel(
                                          number: e.key,
                                          amount: e.value,
                                        ),
                                      )
                                      .map(
                                        (e) => Card.filled(
                                          clipBehavior: Clip.antiAlias,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              5,
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 5,
                                              horizontal: 5,
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  e.number,
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                                Text(
                                                  e.amount.toString(),
                                                  style: TextStyle(
                                                    color:
                                                        (e.amount >
                                                                int.parse(
                                                                  selectedMatch
                                                                      .brake,
                                                                ))
                                                            ? Colors.red
                                                            : Colors.green,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      )
                                      .toList(),
                            ),
                          ),
                          Divider(),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "Total",
                                  style: Theme.of(context).textTheme.titleMedium
                                      ?.copyWith(fontWeight: FontWeight.w600),
                                ),
                                Text(
                                  FormatUtils.formatNumber(
                                    ledger.values.fold(0, (p, c) => p + c),
                                  ),
                                  style: AppTypography.amountMedium,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
                error:
                    (error, stackTrace) => Text('Error: ${error.toString()}'),
                loading: () => const Center(child: CircularProgressIndicator()),
              ),
          error: (error, stackTrace) => Text('Error: ${error.toString()}'),
          loading: () => const Center(child: CircularProgressIndicator()),
        );
  }

  Widget _buildNoMatchSelectedState() {
    return AppScaffold(
      title: 'Ledger',
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_month_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Please select a match',
              style: AppTypography.headline4.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Go to Matches screen to select a match',
              style: AppTypography.bodyMedium.copyWith(color: Colors.grey[500]),
            ),
          ],
        ),
      ),
    );
  }
}
