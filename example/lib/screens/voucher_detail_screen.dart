import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:example/data/models/bet.dart';
import 'package:example/data/models/fighter.dart';
import 'package:example/data/providers/simple_providers.dart';
import 'package:example/theme/app_colors.dart';
import 'package:example/theme/app_typography.dart';
import 'package:example/util/format_utils.dart';
import 'package:example/util/util.dart';
import 'package:example/widgets/scaffold/app_scaffold.dart';

/// Screen that displays detailed information about a voucher
class VoucherDetailScreen extends ConsumerWidget {
  final String timestamp;
  final List<Bet> vouchers;

  const VoucherDetailScreen({
    super.key,
    required this.timestamp,
    required this.vouchers,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Calculate total amount
    int totalAmount =
        multiGenerateTwo(
          vouchers.map((e) => "${e.number}=${e.amount}").join('\n'),
        ).total;

    // Get fighter details
    final fighterId = vouchers.first.fighter;
    final fighterDetails = ref.watch(
      FutureProvider<Fighter?>((ref) async {
        final fighterService = await ref.watch(fighterServiceProvider.future);
        return fighterService.getFighterById(fighterId);
      }),
    );

    // Format timestamp for display
    DateTime dateTime;
    try {
      dateTime = DateTime.parse(timestamp);
    } catch (e) {
      // Handle invalid timestamp format
      dateTime = DateTime.now();
    }
    final String formattedTime = DateFormat('h:mm a').format(dateTime);
    final String shortDate = DateFormat('MMM dd').format(dateTime);

    return AppScaffold(
      title: 'Voucher Details',
      actions: [
        // Copy button
        IconButton(
          icon: const Icon(Icons.content_copy),
          tooltip: 'Copy Voucher',
          onPressed: () => _copyVoucher(context),
        ),
        // Edit button
        IconButton(
          icon: const Icon(Icons.edit),
          tooltip: 'Edit Voucher',
          onPressed: () => _editVoucher(context, ref),
        ),
      ],
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Header card with voucher info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header row with voucher ID and total amount
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Voucher #${vouchers.first.id}',
                          style: AppTypography.headline4,
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Text(
                            FormatUtils.formatNumber(totalAmount),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text('Date: $shortDate', style: AppTypography.bodyMedium),
                    Text(
                      'Time: $formattedTime',
                      style: AppTypography.bodyMedium,
                    ),
                    const SizedBox(height: 8),

                    fighterDetails.when(
                      data:
                          (fighter) =>
                              fighter != null
                                  ? Row(
                                    children: [
                                      Icon(
                                        Icons.person,
                                        color:
                                            fighter.isDealer
                                                ? AppColors.error
                                                : AppColors.primary,
                                        size: 20,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Fighter: ${fighter.name}',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color:
                                              fighter.isDealer
                                                  ? AppColors.error
                                                  : null,
                                        ),
                                      ),
                                    ],
                                  )
                                  : const Text('Unknown Fighter'),
                      loading: () => const Text('Loading fighter...'),
                      error: (_, __) => const Text('Error loading fighter'),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Total Bets: ${vouchers.length}',
                      style: AppTypography.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),

            // Divider with labels
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: const [
                  Expanded(
                    flex: 3,
                    child: Text(
                      'Number',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Amount',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.end,
                    ),
                  ),
                ],
              ),
            ),

            const Divider(),

            // Bet list
            Expanded(
              child: ListView.separated(
                itemCount: vouchers.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final bet = vouchers[index];
                  return _buildBetItem(bet);
                },
              ),
            ),

            // Summary footer
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                border: Border(top: BorderSide(color: Colors.grey.shade300)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Total Amount:',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  Text(
                    FormatUtils.formatNumber(totalAmount),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBetItem(Bet bet) {
    final amount = bet.amount;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              bet.number,
              style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              amount,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: AppColors.primary,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  // Copy voucher to clipboard
  void _copyVoucher(BuildContext context) {
    // Build a formatted string with all bet information
    final StringBuffer buffer = StringBuffer();

    // Add header
    buffer.writeln('📋 Voucher Details');
    buffer.writeln('═══════════════════');

    // Add timestamp
    try {
      final dateTime = DateTime.parse(timestamp);
      final formattedDate = DateFormat('MMMM dd, yyyy').format(dateTime);
      final formattedTime = DateFormat('h:mm a').format(dateTime);
      buffer.writeln('📅 Date: $formattedDate');
      buffer.writeln('🕐 Time: $formattedTime');
    } catch (e) {
      // Handle invalid timestamp
    }

    buffer.writeln('🎯 Voucher ID: ${vouchers.first.id}');
    buffer.writeln('');

    // Calculate total using the same method as UI
    final totalAmount =
        multiGenerateTwo(
          vouchers.map((e) => "${e.number}=${e.amount}").join('\n'),
        ).total;

    // Add bets
    buffer.writeln('📊 Bet Details:');
    buffer.writeln('───────────────');
    for (final bet in vouchers) {
      buffer.writeln('${bet.number} → ${bet.amount}');
    }

    buffer.writeln('');
    buffer.writeln('💰 Total Amount: ${FormatUtils.formatNumber(totalAmount)}');
    buffer.writeln('📈 Total Bets: ${vouchers.length}');

    // Copy to clipboard
    Clipboard.setData(ClipboardData(text: buffer.toString()));

    // Show success feedback
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Voucher details copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  // Edit voucher implementation
  void _editVoucher(BuildContext context, WidgetRef ref) {
    // In a real implementation, you would navigate to an edit screen
    // or show a dialog to edit the voucher

    // For now, just show a snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Edit functionality will be implemented soon'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
