import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:example/data/models/bet.dart';
import 'package:example/data/models/fighter.dart';
import 'package:example/data/providers/simple_providers.dart';
import 'package:example/screens/voucher_detail_screen.dart';
import 'package:example/util/format_utils.dart';
import 'package:example/widgets/scaffold/app_scaffold.dart';


import '../util/util.dart';

/// Sort order for vouchers

/// Screen that displays vouchers (bets grouped by timestamp)
class VoucherScreenNew extends ConsumerStatefulWidget {
  const VoucherScreenNew({super.key});

  @override
  ConsumerState<VoucherScreenNew> createState() => _VoucherScreenNewState();
}

class _VoucherScreenNewState extends ConsumerState<VoucherScreenNew> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final vouchers = ref.watch(filteredVouchersProvider);
    final filterState = ref.watch(voucherFilterProvider);

    return AppScaffold(
      title: 'Vouchers',
      backgroundColor: colorScheme.surface,
      actions: [
        // Filter button with modern design
        Container(
          margin: const EdgeInsets.only(right: 8),
          child: IconButton.filledTonal(
            icon: Icon(Icons.tune_rounded, size: 20),
            tooltip: 'Filter & Sort',
            style: IconButton.styleFrom(
              backgroundColor:
                  filterState.selectedFighterIds.isNotEmpty ||
                          filterState.onlyDealers
                      ? colorScheme.primaryContainer
                      : colorScheme.surfaceContainerHighest,
              foregroundColor:
                  filterState.selectedFighterIds.isNotEmpty ||
                          filterState.onlyDealers
                      ? colorScheme.onPrimaryContainer
                      : colorScheme.onSurface,
            ),
            onPressed: () => _showFilterBottomSheet(),
          ),
        ),
      ],
      body: Column(
        children: [
          // Active filters indicator with modern design
          if (filterState.selectedFighterIds.isNotEmpty ||
              filterState.onlyDealers)
            _buildActiveFiltersBar(filterState),

          // Vouchers list
          Expanded(
            child: vouchers.when(
              data: (data) => _buildVouchersList(data, filterState),
              loading: () => _buildLoadingState(),
              error: (error, stackTrace) => _buildErrorState(error.toString()),
            ),
          ),
        ],
      ),
    );
  }

  // Removed unused _buildMatchInfo method

  Widget _buildActiveFiltersBar(VoucherFilterState filterState) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 0),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.filter_alt_rounded, size: 18, color: colorScheme.primary),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _getActiveFiltersText(filterState),
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          FilledButton.tonal(
            style: FilledButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              minimumSize: const Size(0, 32),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              backgroundColor: colorScheme.surface,
              foregroundColor: colorScheme.onSurface,
            ),
            onPressed: () {
              ref.read(voucherFilterProvider.notifier).state =
                  const VoucherFilterState();
            },
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.clear_rounded, size: 16),
                const SizedBox(width: 4),
                const Text('Clear'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getActiveFiltersText(VoucherFilterState filterState) {
    final List<String> filters = [];

    // Sort order
    filters.add(
      filterState.sortOrder == VoucherSortOrder.newestFirst
          ? 'Newest First'
          : 'Oldest First',
    );

    // Fighter filter
    if (filterState.selectedFighterIds.isNotEmpty) {
      filters.add('${filterState.selectedFighterIds.length} Fighters');
    }

    // Dealer filter
    if (filterState.onlyDealers) {
      filters.add('Only Dealers');
    }

    return filters.join(' • ');
  }

  Widget _buildLoadingState() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: colorScheme.primary, strokeWidth: 3),
          const SizedBox(height: 24),
          Text(
            'Loading vouchers...',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.errorContainer.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                Icons.error_outline_rounded,
                size: 48,
                color: colorScheme.error,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Something went wrong',
              style: theme.textTheme.titleLarge?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(VoucherFilterState filterState) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final hasFilters =
        filterState.selectedFighterIds.isNotEmpty || filterState.onlyDealers;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withValues(
                  alpha: 0.5,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                hasFilters
                    ? Icons.filter_alt_off_rounded
                    : Icons.receipt_long_rounded,
                size: 64,
                color: colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              hasFilters ? 'No vouchers match filters' : 'No vouchers yet',
              style: theme.textTheme.titleLarge?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              hasFilters
                  ? 'Try adjusting your filters to see more results'
                  : 'Vouchers will appear here when you create bets',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            if (hasFilters) ...[
              const SizedBox(height: 24),
              FilledButton.icon(
                icon: const Icon(Icons.clear_all_rounded),
                label: const Text('Clear Filters'),
                onPressed: () {
                  ref.read(voucherFilterProvider.notifier).state =
                      const VoucherFilterState();
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildVouchersList(
    List<Bet> vouchers,
    VoucherFilterState filterState,
  ) {
    if (vouchers.isEmpty) {
      return _buildEmptyState(filterState);
    }

    // Group vouchers by timestamp and fighter
    final Map<String, List<Bet>> groupedVouchers = {};
    for (final voucher in vouchers) {
      // Extract the time part (HH:MM:SS) from the timestamp to group by time and fighter
      DateTime dateTime;
      try {
        dateTime = DateTime.parse(voucher.timestamp);
      } catch (e) {
        // If timestamp can't be parsed, use current time
        dateTime = DateTime.now();
      }
      final String timeKey = DateFormat('HH:mm:ss').format(dateTime);
      final String groupKey = '$timeKey-${voucher.fighter}';

      if (!groupedVouchers.containsKey(groupKey)) {
        groupedVouchers[groupKey] = [];
      }
      groupedVouchers[groupKey]!.add(voucher);
    }

    // Sort the groups based on timestamp
    final sortedKeys = groupedVouchers.keys.toList();
    if (filterState.sortOrder == VoucherSortOrder.newestFirst) {
      sortedKeys.sort((a, b) => b.compareTo(a)); // Descending (newest first)
    } else {
      sortedKeys.sort(); // Ascending (oldest first)
    }

    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
      itemCount: sortedKeys.length,
      itemBuilder: (context, index) {
        final groupKey = sortedKeys[index];
        final voucherGroup = groupedVouchers[groupKey]!;
        return AnimatedContainer(
          duration: Duration(milliseconds: 200 + (index * 50)),
          curve: Curves.easeOutCubic,
          child: _buildVoucherCard(
            voucherGroup.first.timestamp,
            voucherGroup,
            index,
          ),
        );
      },
    );
  }

  Widget _buildVoucherCard(String timestamp, List<Bet> vouchers, int index) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Calculate total amount directly
    int totalAmount =
        multiGenerateTwo(
          vouchers.map((e) => "${e.number}=${e.amount}").join('\n'),
        ).total;

    // Get fighter details for the first voucher (all vouchers in a group have the same fighter)
    final fighterId = vouchers.first.fighter;
    final fighterDetails = ref.watch(fighterDetailsProvider(fighterId));

    // Format timestamp for display
    DateTime dateTime;
    try {
      dateTime = DateTime.parse(timestamp);
    } catch (e) {
      // Handle invalid timestamp format
      dateTime = DateTime.now();
    }
    final String formattedTime = DateFormat('h:mm a').format(dateTime);
    final String formattedDate = DateFormat('MMM dd').format(dateTime);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        elevation: 0,
        borderRadius: BorderRadius.circular(16),
        color: colorScheme.surface,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder:
                    (context) => VoucherDetailScreen(
                      timestamp: timestamp,
                      vouchers: vouchers,
                    ),
              ),
            );
          },
          onLongPress: () async {
            // Handle long press to delete voucher group
            final shouldDelete = await showDialog<bool>(
              context: context,
              builder:
                  (context) => AlertDialog(
                    title: const Text('Delete Voucher'),
                    content: const Text(
                      'Are you sure you want to delete this voucher?',
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('Cancel'),
                      ),
                      FilledButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        style: FilledButton.styleFrom(
                          backgroundColor: colorScheme.error,
                          foregroundColor: colorScheme.onError,
                        ),
                        child: const Text('Delete'),
                      ),
                    ],
                  ),
            );

            if (shouldDelete == true) {
              final betService = await ref.read(betServiceProvider.future);
              for (var voucher in vouchers) {
                await betService.deleteBet(voucher.id);
              }
              final _ = ref.refresh(betServiceProvider);
            }
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: colorScheme.outline.withValues(alpha: 0.2),
                width: 1,
              ),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  colorScheme.surface,
                  colorScheme.surfaceContainerLowest,
                ],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with timestamp and fighter info
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        colorScheme.primaryContainer.withValues(alpha: 0.3),
                        colorScheme.primaryContainer.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: colorScheme.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.receipt_long_rounded,
                          color: colorScheme.primary,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  formattedTime,
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: colorScheme.outline.withValues(
                                      alpha: 0.1,
                                    ),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    formattedDate,
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: colorScheme.onSurface.withValues(
                                        alpha: 0.7,
                                      ),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            fighterDetails.when(
                              data:
                                  (fighter) =>
                                      fighter != null
                                          ? Row(
                                            children: [
                                              Icon(
                                                fighter.isDealer
                                                    ? Icons.store_rounded
                                                    : Icons.person_rounded,
                                                size: 16,
                                                color:
                                                    fighter.isDealer
                                                        ? colorScheme.error
                                                        : colorScheme.onSurface
                                                            .withValues(
                                                              alpha: 0.7,
                                                            ),
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                fighter.name,
                                                style: theme
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.copyWith(
                                                      color:
                                                          fighter.isDealer
                                                              ? colorScheme
                                                                  .error
                                                              : colorScheme
                                                                  .onSurface
                                                                  .withValues(
                                                                    alpha: 0.7,
                                                                  ),
                                                      fontWeight:
                                                          fighter.isDealer
                                                              ? FontWeight.w600
                                                              : FontWeight.w500,
                                                    ),
                                              ),
                                              if (fighter.isDealer) ...[
                                                const SizedBox(width: 8),
                                                Container(
                                                  padding:
                                                      const EdgeInsets.symmetric(
                                                        horizontal: 6,
                                                        vertical: 2,
                                                      ),
                                                  decoration: BoxDecoration(
                                                    color:
                                                        colorScheme
                                                            .errorContainer,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          6,
                                                        ),
                                                  ),
                                                  child: Text(
                                                    'DEALER',
                                                    style: theme
                                                        .textTheme
                                                        .bodySmall
                                                        ?.copyWith(
                                                          color:
                                                              colorScheme
                                                                  .onErrorContainer,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          fontSize: 10,
                                                        ),
                                                  ),
                                                ),
                                              ],
                                            ],
                                          )
                                          : Text(
                                            'Unknown Fighter',
                                            style: theme.textTheme.bodyMedium
                                                ?.copyWith(
                                                  color: colorScheme.onSurface
                                                      .withValues(alpha: 0.5),
                                                ),
                                          ),
                              loading:
                                  () => Row(
                                    children: [
                                      SizedBox(
                                        width: 12,
                                        height: 12,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          color: colorScheme.onSurface
                                              .withValues(alpha: 0.5),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Loading...',
                                        style: theme.textTheme.bodyMedium
                                            ?.copyWith(
                                              color: colorScheme.onSurface
                                                  .withValues(alpha: 0.5),
                                            ),
                                      ),
                                    ],
                                  ),
                              error:
                                  (_, __) => Text(
                                    'Error loading fighter',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: colorScheme.error,
                                    ),
                                  ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              colorScheme.primary,
                              colorScheme.primary.withValues(alpha: 0.8),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: colorScheme.primary.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Text(
                          FormatUtils.formatNumber(totalAmount),
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: colorScheme.onPrimary,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Preview of bets (show only first 3)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 16,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.list_alt_rounded,
                            size: 16,
                            color: colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Bets (${vouchers.length})',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface.withValues(
                                alpha: 0.7,
                              ),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      ...vouchers.take(3).map((bet) => _buildBetItem(bet)),
                    ],
                  ),
                ),

                // "See more" indicator if there are more than 3 bets
                if (vouchers.length > 3)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    decoration: BoxDecoration(
                      color: colorScheme.primaryContainer.withValues(
                        alpha: 0.3,
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: colorScheme.primary.withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.expand_more_rounded,
                          size: 18,
                          color: colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'See ${vouchers.length - 3} more bets',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),

                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBetItem(Bet bet) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              bet.number,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: colorScheme.primary,
              ),
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: colorScheme.secondaryContainer.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              bet.amount,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: colorScheme.onSecondaryContainer,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterBottomSheet() {
    if (!mounted) return;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => _FilterBottomSheet(
            onApply: () {
              if (mounted) {
                setState(() {});
              }
            },
          ),
    );
  }
}

/// Modern filter bottom sheet for vouchers
class _FilterBottomSheet extends ConsumerStatefulWidget {
  final VoidCallback onApply;

  const _FilterBottomSheet({required this.onApply});

  @override
  ConsumerState<_FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends ConsumerState<_FilterBottomSheet> {
  late VoucherSortOrder _tempSortOrder;
  late List<int> _tempSelectedFighterIds;
  late bool _tempOnlyDealers;

  @override
  void initState() {
    super.initState();
    final currentFilterState = ref.read(voucherFilterProvider);
    _tempSortOrder = currentFilterState.sortOrder;
    _tempSelectedFighterIds = List.from(currentFilterState.selectedFighterIds);
    _tempOnlyDealers = currentFilterState.onlyDealers;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final allFighters = ref.watch(allFightersProvider);

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.85,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: colorScheme.outline.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(24.0),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primaryContainer.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.tune_rounded,
                    color: colorScheme.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'Filter & Sort',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton.filledTonal(
                  icon: const Icon(Icons.close_rounded),
                  onPressed: () => Navigator.of(context).pop(),
                  style: IconButton.styleFrom(
                    backgroundColor: colorScheme.surfaceContainerHighest,
                  ),
                ),
              ],
            ),
          ),

          // Dialog content
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    // Sort order
                    const Text(
                      'Sort Order',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    RadioListTile<VoucherSortOrder>(
                      title: const Text('Newest First'),
                      value: VoucherSortOrder.newestFirst,
                      groupValue: _tempSortOrder,
                      contentPadding: EdgeInsets.zero,
                      dense: true,
                      onChanged: (value) {
                        setState(() {
                          _tempSortOrder = value!;
                        });
                      },
                    ),
                    RadioListTile<VoucherSortOrder>(
                      title: const Text('Oldest First'),
                      value: VoucherSortOrder.oldestFirst,
                      groupValue: _tempSortOrder,
                      contentPadding: EdgeInsets.zero,
                      dense: true,
                      onChanged: (value) {
                        setState(() {
                          _tempSortOrder = value!;
                        });
                      },
                    ),
                    const Divider(),

                    // Only dealers checkbox
                    CheckboxListTile(
                      title: const Text('Show Only Dealers'),
                      value: _tempOnlyDealers,
                      contentPadding: EdgeInsets.zero,
                      dense: true,
                      onChanged: (value) {
                        setState(() {
                          _tempOnlyDealers = value!;
                        });
                      },
                    ),
                    const Divider(),

                    // Fighter selection
                    const Text(
                      'Filter by Fighters',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),

                    // Fighter list
                    allFighters.when(
                      data: (fighters) => _buildFighterList(fighters),
                      loading:
                          () => const Center(
                            child: Padding(
                              padding: EdgeInsets.all(16.0),
                              child: CircularProgressIndicator(),
                            ),
                          ),
                      error: (_, __) => const Text('Error loading fighters'),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
          ),

          // Dialog actions
          const Divider(height: 1),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    // Apply the filters
                    ref
                        .read(voucherFilterProvider.notifier)
                        .state = VoucherFilterState(
                      sortOrder: _tempSortOrder,
                      selectedFighterIds: _tempSelectedFighterIds,
                      onlyDealers: _tempOnlyDealers,
                    );
                    widget.onApply();
                    Navigator.of(context).pop();
                  },
                  child: const Text('Apply'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFighterList(List<Fighter> fighters) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Select all / clear all
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TextButton(
              onPressed: () {
                setState(() {
                  _tempSelectedFighterIds = fighters.map((f) => f.id).toList();
                });
              },
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                minimumSize: const Size(0, 36),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: const Text('Select All'),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _tempSelectedFighterIds = [];
                });
              },
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                minimumSize: const Size(0, 36),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: const Text('Clear All'),
            ),
          ],
        ),

        // Fighter list
        Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.3,
          ),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: fighters.length,
            itemBuilder: (context, index) {
              final fighter = fighters[index];
              return CheckboxListTile(
                title: Text(
                  fighter.name,
                  style: TextStyle(
                    color:
                        fighter.isDealer
                            ? Theme.of(context).colorScheme.error
                            : null,
                    fontWeight: fighter.isDealer ? FontWeight.bold : null,
                    fontSize: 14,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                secondary: Icon(
                  fighter.isDealer ? Icons.store : Icons.person,
                  color:
                      fighter.isDealer
                          ? Theme.of(context).colorScheme.error
                          : Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.7),
                  size: 20,
                ),
                value: _tempSelectedFighterIds.contains(fighter.id),
                dense: true,
                contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                controlAffinity: ListTileControlAffinity.leading,
                onChanged: (value) {
                  setState(() {
                    if (value!) {
                      _tempSelectedFighterIds.add(fighter.id);
                    } else {
                      _tempSelectedFighterIds.remove(fighter.id);
                    }
                  });
                },
              );
            },
          ),
        ),

        // Selected count
        if (_tempSelectedFighterIds.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              '${_tempSelectedFighterIds.length} fighters selected',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }
}
