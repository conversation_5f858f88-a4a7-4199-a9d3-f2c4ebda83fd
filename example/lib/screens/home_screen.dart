import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rauth/rauth_exports.dart';
import 'package:example/screens/all_sale_screen.dart';
import 'package:example/screens/ledger_screen.dart';

import '../theme/app_colors.dart';
import '../widgets/scaffold/app_scaffold.dart';
import '../widgets/drawer/app_drawer.dart';
import 'add_bet_screen.dart';
import 'match_list_screen.dart';
import 'fighter_list_screen.dart';
import 'settlement_screen.dart';
import 'voucher_screen_new.dart';

/// A home screen that provides navigation to the main sections of the app
class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return AppScaffold(
      title: 'rPOS',
      drawer: const AppDrawer(),
      actions: [TimeLeftText(), ProfileButton()],
      showBackButton: false,
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: Text('Dashboard', style: theme.textTheme.headlineMedium),
            ),
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                childAspectRatio: 1,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                children: [
                  _buildNavigationCard(
                    context,
                    title: 'Fighters',
                    icon: Icons.people,
                    color: AppColors.success,
                    onTap:
                        () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const FighterListScreen(),
                          ),
                        ),
                  ),
                  _buildNavigationCard(
                    context,
                    title: 'All Sale',
                    icon: Icons.sell_outlined,
                    color: AppColors.primary,
                    onTap:
                        () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => AllSaleScreen(),
                          ),
                        ),
                  ),
                  _buildNavigationCard(
                    context,
                    title: 'Add Bets',
                    icon: Icons.confirmation_number,
                    color: AppColors.secondary,
                    onTap:
                        () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const AddBetScreen(),
                          ),
                        ),
                  ),
                  _buildNavigationCard(
                    context,
                    title: 'Ledger',
                    icon: Icons.table_view_sharp,
                    color: AppColors.info,
                    onTap:
                        () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const LedgerScreen(),
                          ),
                        ),
                  ),
                  _buildNavigationCard(
                    context,
                    title: 'Vouchers',
                    icon: Icons.receipt_long,
                    color: AppColors.warning,
                    onTap:
                        () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const VoucherScreenNew(),
                          ),
                        ),
                  ),
                  _buildNavigationCard(
                    context,
                    title: 'Settlement',
                    icon: Icons.account_balance_wallet,
                    color: AppColors.info,
                    onTap:
                        () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SettlementScreen(),
                          ),
                        ),
                  ),
                  _buildNavigationCard(
                    context,
                    title: 'Matches',
                    icon: Icons.calendar_month,
                    color: AppColors.primary,
                    onTap:
                        () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const MatchListScreen(),
                          ),
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withAlpha(40),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, size: 40, color: color),
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
